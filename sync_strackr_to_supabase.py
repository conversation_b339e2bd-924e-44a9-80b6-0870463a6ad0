#!/usr/bin/env python3
"""
Local Strackr to Supabase Sync Script

This script fetches all historical Strackr transaction data and uploads it to Supabase.
It uses local environment variables from .env file instead of Google Secret Manager.

Usage:
    python sync_strackr_to_supabase.py [--days-back 30] [--batch-size 100]
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from dotenv import load_dotenv

# Add the dags directory to Python path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "dags"))

from dependencies.transaction_reports.strackr_client import StrackrClient
from dependencies.transaction_reports.strackr_transform import (
    transform_strackr_transactions,
)
from dependencies.transaction_reports.schema import NormalizedTransaction
from dependencies.supabase_client import SupabaseClientLocal
from dependencies.transaction_reports.utils import get_date_range_for_days_back

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class LocalStrackrAuth:
    """Local version of StrackrAuth that uses environment variables."""

    def __init__(self):
        load_dotenv()
        self._api_id = os.getenv("STRACKR_API_ID")
        self._api_key = os.getenv("STRACKR_API_KEY")
        self.base_url = "https://api.strackr.com/v4"

        if not self._api_id or not self._api_key:
            raise ValueError(
                "STRACKR_API_ID and STRACKR_API_KEY must be set in .env file"
            )

    @property
    def api_id(self) -> str:
        """Get API ID."""
        return self._api_id

    @property
    def api_key(self) -> str:
        """Get API Key."""
        return self._api_key

    def get_auth_params(self) -> dict:
        """Get authentication parameters for Strackr API requests."""
        return {"api_id": self.api_id, "api_key": self.api_key}

    def get_authenticated_url(self, endpoint: str) -> str:
        """Get URL with authentication parameters."""
        return f"{self.base_url}{endpoint}"

    def validate_credentials(self) -> bool:
        """Validate that credentials are present."""
        return bool(self.api_id and self.api_key)


class LocalStrackrClient(StrackrClient):
    """Local version of StrackrClient that uses environment auth."""

    def __init__(self):
        """Initialize with local auth instead of Secret Manager."""
        import requests
        from dependencies.transaction_reports.constants import REQUEST_TIMEOUT

        self.auth = LocalStrackrAuth()
        self.session = requests.Session()
        self.session.timeout = REQUEST_TIMEOUT

        if not self.auth.validate_credentials():
            raise Exception("Invalid Strackr API credentials")


def fetch_all_strackr_data(days_back: int = 30) -> List[Dict[str, Any]]:
    """
    Fetch all Strackr transaction data for the specified number of days.

    Args:
        days_back: Number of days back to fetch data for

    Returns:
        List of raw transaction dictionaries
    """
    logger.info(f"Fetching Strackr data for the last {days_back} days")

    # Get date range
    start_date, end_date = get_date_range_for_days_back(days_back)
    logger.info(f"Date range: {start_date} to {end_date}")

    # Initialize client
    client = LocalStrackrClient()

    # Fetch transactions
    raw_transactions = client.get_transactions(
        start_date=start_date,
        end_date=end_date,
        currency="USD",  # As per your preference
    )

    logger.info(f"Fetched {len(raw_transactions)} raw transactions")
    return raw_transactions


def transform_and_upload_data(
    raw_transactions: List[Dict[str, Any]], batch_size: int = 100
) -> Dict[str, Any]:
    """
    Transform raw transactions and upload to Supabase.

    Args:
        raw_transactions: List of raw transaction dictionaries
        batch_size: Number of transactions to upload per batch

    Returns:
        Upload results summary
    """
    logger.info(f"Transforming {len(raw_transactions)} transactions")

    # Transform to normalized schema
    normalized_transactions = transform_strackr_transactions(raw_transactions)
    logger.info(
        f"Transformed to {len(normalized_transactions)} normalized transactions"
    )

    # Initialize Supabase client
    supabase_client = SupabaseClientLocal()

    # Upload to Supabase
    logger.info(f"Uploading to Supabase in batches of {batch_size}")
    result = supabase_client.insert_transactions(
        normalized_transactions,
        batch_size=batch_size,
        on_conflict="ignore",  # Skip duplicates
    )

    return result


def main():
    """Main function to run the sync process."""
    parser = argparse.ArgumentParser(description="Sync Strackr data to Supabase")
    parser.add_argument(
        "--days-back",
        type=int,
        default=30,
        help="Number of days back to fetch data (default: 30)",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=100,
        help="Batch size for Supabase uploads (default: 100)",
    )
    parser.add_argument(
        "--setup-schema",
        action="store_true",
        help="Set up Supabase schema before syncing",
    )

    args = parser.parse_args()

    try:
        # Load environment variables
        load_dotenv()
        logger.info("Loaded environment variables from .env file")

        # Set up schema if requested
        if args.setup_schema:
            logger.info("Setting up Supabase schema...")
            supabase_client = SupabaseClientLocal()
            success = supabase_client.create_table_if_not_exists()
            if success:
                logger.info("✅ Schema setup successful")
            else:
                logger.error("❌ Schema setup failed")
                return 1

        # Fetch data from Strackr
        logger.info("🔄 Starting Strackr data fetch...")
        raw_transactions = fetch_all_strackr_data(args.days_back)

        if not raw_transactions:
            logger.warning("⚠️  No transactions found")
            return 0

        # Transform and upload
        logger.info("🔄 Starting transformation and upload...")
        result = transform_and_upload_data(raw_transactions, args.batch_size)

        # Print results
        if result["success"]:
            logger.info("✅ Sync completed successfully!")
            logger.info(f"📊 Results:")
            logger.info(
                f"   - Total transactions processed: {result['total_processed']}"
            )
            logger.info(f"   - Successfully inserted: {result['inserted_count']}")
            logger.info(f"   - Errors: {result['error_count']}")

            if result["errors"]:
                logger.warning(f"⚠️  Some errors occurred:")
                for error in result["errors"][:5]:  # Show first 5 errors
                    logger.warning(f"   - {error}")
        else:
            logger.error("❌ Sync failed!")
            logger.error(f"Errors: {result['errors']}")
            return 1

    except Exception as e:
        logger.error(f"❌ Fatal error: {str(e)}")
        return 1

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
